# TODO:

- [x] create-peer-grading-detail: <PERSON><PERSON><PERSON> komponen PeerGradingDetail.tsx (priority: High)
- [x] modify-daftar-peer-grading: Modifikasi DaftarPeerGrading.tsx untuk hanya menampilkan pemilihan semester dan mata kuliah (priority: High)
- [x] add-navigation-logic: Tambahkan logika navigasi dengan parameter semester dan mata kuliah (priority: High)
- [x] setup-routing: Siapkan routing untuk halaman detail peer grading (priority: Medium)
- [x] test-navigation: Test navigasi dan transfer data antar halaman (priority: Medium)
