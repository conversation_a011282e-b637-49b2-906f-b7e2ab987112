<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\HasMany;

class PeerGradingCategory extends Model
{
    use HasFactory;

    protected $table = 'peer_grading_categories';

    protected $fillable = [
        'nama',
        'deskripsi',
        'kode_program_studi',
        'program_studi',
        'bobot',
        'is_active',
        'urutan',
    ];

    protected $casts = [
        'is_active' => 'boolean',
        'bobot' => 'integer',
        'urutan' => 'integer',
    ];

    /**
     * Get all ratings for this category
     */
    public function ratings(): HasMany
    {
        return $this->hasMany(PeerGradingRating::class, 'category_id');
    }

    /**
     * Scope for active categories
     */
    public function scopeActive($query)
    {
        return $query->where('is_active', true);
    }

    /**
     * Scope for categories by program studi
     */
    public function scopeByProgramStudi($query, $kodeProgramStudi)
    {
        return $query->where('kode_program_studi', $kodeProgramStudi);
    }

    /**
     * Scope for ordering by urutan
     */
    public function scopeOrdered($query)
    {
        return $query->orderBy('urutan', 'asc');
    }

    /**
     * Get categories for specific program studi
     */
    public static function getByProgramStudi($kodeProgramStudi)
    {
        return self::active()
            ->byProgramStudi($kodeProgramStudi)
            ->ordered()
            ->get();
    }

    /**
     * Get average rating for this category in a specific session
     */
    public function getAverageRatingForSession($sessionId): float
    {
        return $this->ratings()
            ->where('session_id', $sessionId)
            ->avg('rating') ?? 0;
    }

    /**
     * Get rating distribution for this category in a specific session
     */
    public function getRatingDistributionForSession($sessionId): array
    {
        $ratings = $this->ratings()
            ->where('session_id', $sessionId)
            ->selectRaw('rating, COUNT(*) as count')
            ->groupBy('rating')
            ->pluck('count', 'rating')
            ->toArray();

        // Ensure all rating values are present
        $distribution = [];
        for ($i = -2; $i <= 2; $i++) {
            $distribution[$i] = $ratings[$i] ?? 0;
        }

        return $distribution;
    }
}