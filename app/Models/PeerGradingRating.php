<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

class PeerGradingRating extends Model
{
    use HasFactory;

    protected $table = 'peer_grading_ratings';

    protected $fillable = [
        'session_id',
        'category_id',
        'penilai_id',
        'penilai_nim',
        'penilai_nama',
        'dinilai_id',
        'dinilai_nim',
        'dinilai_nama',
        'rating',
        'komentar',
        'tanggal_penilaian',
    ];

    protected $casts = [
        'tanggal_penilaian' => 'datetime',
        'rating' => 'integer',
    ];

    // Rating constants
    const RATING_SANGAT_KURANG = -2;
    const RATING_KURANG = -1;
    const RATING_CUKUP = 0;
    const RATING_BAIK = 1;
    const RATING_SANGAT_BAIK = 2;

    /**
     * Get the session that owns this rating
     */
    public function session(): BelongsTo
    {
        return $this->belongsTo(PeerGradingSession::class, 'session_id');
    }

    /**
     * Get the category that owns this rating
     */
    public function category(): BelongsTo
    {
        return $this->belongsTo(PeerGradingCategory::class, 'category_id');
    }

    /**
     * Scope for ratings by session
     */
    public function scopeBySession($query, $sessionId)
    {
        return $query->where('session_id', $sessionId);
    }

    /**
     * Scope for ratings by penilai
     */
    public function scopeByPenilai($query, $penilaiId)
    {
        return $query->where('penilai_id', $penilaiId);
    }

    /**
     * Scope for ratings by dinilai
     */
    public function scopeByDinilai($query, $dinilaiId)
    {
        return $query->where('dinilai_id', $dinilaiId);
    }

    /**
     * Scope for ratings by category
     */
    public function scopeByCategory($query, $categoryId)
    {
        return $query->where('category_id', $categoryId);
    }

    /**
     * Get rating label
     */
    public function getRatingLabel(): string
    {
        return self::getRatingLabels()[$this->rating] ?? 'Unknown';
    }

    /**
     * Get all rating labels
     */
    public static function getRatingLabels(): array
    {
        return [
            self::RATING_SANGAT_KURANG => 'Sangat Kurang',
            self::RATING_KURANG => 'Kurang',
            self::RATING_CUKUP => 'Cukup',
            self::RATING_BAIK => 'Baik',
            self::RATING_SANGAT_BAIK => 'Sangat Baik',
        ];
    }

    /**
     * Get rating values
     */
    public static function getRatingValues(): array
    {
        return [
            self::RATING_SANGAT_KURANG,
            self::RATING_KURANG,
            self::RATING_CUKUP,
            self::RATING_BAIK,
            self::RATING_SANGAT_BAIK,
        ];
    }

    /**
     * Validate rating value
     */
    public static function isValidRating($rating): bool
    {
        return in_array($rating, self::getRatingValues());
    }

    /**
     * Get ratings for a specific member in a session
     */
    public static function getRatingsForMember($sessionId, $dinilaiId)
    {
        return self::bySession($sessionId)
            ->byDinilai($dinilaiId)
            ->with('category')
            ->get()
            ->groupBy('category_id');
    }

    /**
     * Get average rating for a member in a session
     */
    public static function getAverageRatingForMember($sessionId, $dinilaiId): float
    {
        return self::bySession($sessionId)
            ->byDinilai($dinilaiId)
            ->avg('rating') ?? 0;
    }

    /**
     * Get ratings given by a specific member
     */
    public static function getRatingsByPenilai($sessionId, $penilaiId)
    {
        return self::bySession($sessionId)
            ->byPenilai($penilaiId)
            ->with(['category', 'session'])
            ->get();
    }

    /**
     * Check if a rating already exists
     */
    public static function ratingExists($sessionId, $categoryId, $penilaiId, $dinilaiId): bool
    {
        return self::where([
            'session_id' => $sessionId,
            'category_id' => $categoryId,
            'penilai_id' => $penilaiId,
            'dinilai_id' => $dinilaiId,
        ])->exists();
    }
}