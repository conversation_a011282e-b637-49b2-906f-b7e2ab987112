<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Carbon\Carbon;

class PeerGradingSession extends Model
{
    use HasFactory;

    protected $table = 'peer_grading_sessions';

    protected $fillable = [
        'id_kelompok',
        'id_kelas',
        'id_program_studi_siakad',
        'nama_sesi',
        'deskripsi',
        'tanggal_mulai',
        'tanggal_selesai',
        'status',
        'is_active',
    ];

    protected $casts = [
        'tanggal_mulai' => 'datetime',
        'tanggal_selesai' => 'datetime',
        'is_active' => 'boolean',
    ];

    // Status constants
    const STATUS_DRAFT = 'draft';
    const STATUS_ACTIVE = 'active';
    const STATUS_COMPLETED = 'completed';
    const STATUS_CANCELLED = 'cancelled';

    /**
     * Get all ratings for this session
     */
    public function ratings(): HasMany
    {
        return $this->hasMany(PeerGradingRating::class, 'session_id');
    }

    /**
     * Get all comments for this session
     */
    public function comments(): HasMany
    {
        return $this->hasMany(PeerGradingComment::class, 'session_id');
    }

    /**
     * Get all submissions for this session
     */
    public function submissions(): HasMany
    {
        return $this->hasMany(PeerGradingSubmission::class, 'session_id');
    }

    /**
     * Get all results for this session
     */
    public function results(): HasMany
    {
        return $this->hasMany(PeerGradingResult::class, 'session_id');
    }

    /**
     * Scope for active sessions
     */
    public function scopeActive($query)
    {
        return $query->where('is_active', true);
    }

    /**
     * Scope for sessions by status
     */
    public function scopeByStatus($query, $status)
    {
        return $query->where('status', $status);
    }

    /**
     * Scope for sessions by program studi
     */
    public function scopeByProgramStudi($query, $kodeProgramStudi)
    {
        return $query->where('id_program_studi_siakad', $kodeProgramStudi);
    }

    /**
     * Check if session is currently active (within date range)
     */
    public function isCurrentlyActive(): bool
    {
        $now = Carbon::now();
        return $this->status === self::STATUS_ACTIVE &&
               $this->is_active &&
               $now->between($this->tanggal_mulai, $this->tanggal_selesai);
    }

    /**
     * Check if session has expired
     */
    public function hasExpired(): bool
    {
        return Carbon::now()->isAfter($this->tanggal_selesai);
    }

    /**
     * Get session duration in days
     */
    public function getDurationInDays(): int
    {
        return $this->tanggal_mulai->diffInDays($this->tanggal_selesai);
    }

    /**
     * Get completion percentage based on submissions
     */
    public function getCompletionPercentage(): float
    {
        $totalExpectedSubmissions = $this->getExpectedSubmissionsCount();
        $actualSubmissions = $this->submissions()->where('status', 'submitted')->count();
        
        if ($totalExpectedSubmissions === 0) {
            return 0;
        }
        
        return ($actualSubmissions / $totalExpectedSubmissions) * 100;
    }

    /**
     * Get expected number of submissions (placeholder - should be calculated based on group members)
     */
    private function getExpectedSubmissionsCount(): int
    {
        // This should be calculated based on the number of group members
        // For now, returning a placeholder value
        return 5; // Assuming 5 members per group
    }

    /**
     * Get available statuses
     */
    public static function getAvailableStatuses(): array
    {
        return [
            self::STATUS_DRAFT => 'Draft',
            self::STATUS_ACTIVE => 'Active',
            self::STATUS_COMPLETED => 'Completed',
            self::STATUS_CANCELLED => 'Cancelled',
        ];
    }
}