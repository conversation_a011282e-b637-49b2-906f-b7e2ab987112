import { authOptions } from '@configs/authOptions';
import { SessionUserLogin } from '@data/models/global.model';
import RefactorManagementBagianScreen from '@ui/modules/hris/Presensi/RefactorManagementBagian';
import { Metadata } from 'next';
import { getServerSession } from 'next-auth';
import React from 'react';

export const metadata: Metadata = { 
    title: 'Management Bagian',
};
 
const page = async () => {
    const session: SessionUserLogin | null = await getServerSession(authOptions);

    return <RefactorManagementBagianScreen session={session} />;
};

export default page;
