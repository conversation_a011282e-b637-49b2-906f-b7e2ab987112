import { authOptions } from '@configs/authOptions';
import { SessionUserLogin } from '@data/models/global.model';
import RefactorDataPegawaiScreen from '@ui/modules/hris/Presensi/RefactorDataPegawai';
import { Metadata } from 'next';
import { getServerSession } from 'next-auth';
import React from 'react';

export const metadata: Metadata = { 
    title: 'Refactor Data Pegawai',
};

const page = async () => {
    const session: SessionUserLogin | null = await getServerSession(authOptions);

    return <RefactorDataPegawaiScreen session={session} />;
};

export default page;
