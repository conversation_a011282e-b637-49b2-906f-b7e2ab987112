import { authOptions } from '@configs/authOptions';
import { SessionUserLogin } from '@data/models/global.model';
import RefactorLaporanPresensiScreen from '@ui/modules/hris/Presensi/RefactorLaporanPresensi';
import { Metadata } from 'next';
import { getServerSession } from 'next-auth';
import React from 'react';

export const metadata: Metadata = {
    title: 'Refactor Laporan Presensi',
};

const page = async () => {
    const session: SessionUserLogin | null = await getServerSession(authOptions);

    return <RefactorLaporanPresensiScreen session={session} />;
};

export default page;
