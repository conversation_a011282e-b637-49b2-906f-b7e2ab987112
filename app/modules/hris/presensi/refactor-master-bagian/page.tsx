import { authOptions } from '@configs/authOptions';
import { SessionUserLogin } from '@data/models/global.model';
import RefactorMasterBagianScreen from '@ui/modules/hris/Presensi/RefactorMasterBagian';
import { Metadata } from 'next';
import { getServerSession } from 'next-auth';
import React from 'react';

export const metadata: Metadata = { 
    title: 'Refactor Master Bagian',
};

const page = async () => {
    const session: SessionUserLogin | null = await getServerSession(authOptions);

    return <RefactorMasterBagianScreen session={session} />;
};

export default page;
