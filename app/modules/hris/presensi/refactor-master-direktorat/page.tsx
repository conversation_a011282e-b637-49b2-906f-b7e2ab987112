import { authOptions } from '@configs/authOptions';
import { SessionUserLogin } from '@data/models/global.model';
import RefactorMasterDirektoratScreen from '@ui/modules/hris/Presensi/RefactorMasterDirektorat';
import { Metadata } from 'next';
import { getServerSession } from 'next-auth';
import React from 'react';

export const metadata: Metadata = { 
    title: 'Refactor Master Direktorat',
};

const page = async () => {
    const session: SessionUserLogin | null = await getServerSession(authOptions);

    return <RefactorMasterDirektoratScreen session={session} />;
};

export default page;
