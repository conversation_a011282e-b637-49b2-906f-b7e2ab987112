import { authOptions } from '@configs/authOptions';
import { SessionUserLogin } from '@data/models/global.model';
import RefactorMasterSubBagianScreen from '@ui/modules/hris/Presensi/RefactorMasterSubBagian';
import { Metadata } from 'next';
import { getServerSession } from 'next-auth';
import React from 'react';

export const metadata: Metadata = { 
    title: 'Refactor Master Sub Bagian',
};

const page = async () => {
    const session: SessionUserLogin | null = await getServerSession(authOptions);

    return <RefactorMasterSubBagianScreen session={session} />;
};

export default page;
