import React from 'react';
import { <PERSON>ada<PERSON> } from 'next';
import { <PERSON><PERSON><PERSON> } from 'next/font/google';
import MainLayout from '@templates/MainLayout/MainLayout';

export const metadata: Metadata = {
    title: {
        template: '%s | SIP - Sistem Informasi Paramadina',
        default: 'SIP - Sistem Informasi Paramadina',
    },
};

const nunito = Nunito({
    weight: ['400', '500', '600', '700', '800'],
    subsets: ['latin'],
    display: 'swap',
    variable: '--font-nunito',
});

export default function DefaultLayout({ children }: { children: React.ReactNode }) {
    return (
        <main className={nunito.variable}>
            <MainLayout>{children}</MainLayout>
        </main>
    );
}
