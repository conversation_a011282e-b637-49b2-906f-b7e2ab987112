import { NextAuthOptions } from 'next-auth';
import CredentialsProvider from 'next-auth/providers/credentials';
import { loginSimpulRepository } from '@restApi/auth/login/repository';
import GoogleProvider from 'next-auth/providers/google';

export const authOptions: NextAuthOptions = {
    providers: [
        GoogleProvider({
            clientId: process.env.GOOGLE_ID ?? '',
            clientSecret: process.env.GOOGLE_SECRET ?? '',
            authorization: {
                params: {
                    prompt: 'consent',
                    access_type: 'offline',
                    response_type: 'code',
                },
            },
        }),
        CredentialsProvider({
            id: 'credentials',
            name: 'Credentials',
            credentials: {
                email: { label: 'Username', type: 'text', placeholder: 'username' },
                password: { label: 'Password', type: 'password' },
                login_by: { label: 'Login By', type: 'text' },
            },
            async authorize(credentials, _req) {
                try {
                    const email = credentials?.email ?? '';
                    let scope = 'staff';
                   
                    if (email.includes('@students.paramadina.ac.id')) {
                        scope = 'mahasiswa';
                    } else {
                        scope = 'staff';
                    }
                    
                    const resSimpul = await loginSimpulRepository({
                        email: email,
                        password: credentials?.password ?? '',
                        scope: scope,
                    });

                    const responseSimpul = resSimpul.data;
                    const user: any = {};

                    if (!resSimpul.success) {
                        throw new Error('user.message');
                    }

                    if (resSimpul.success) {
                        user.userSimpul = responseSimpul.user;
                        user.accessTokenSimpul = responseSimpul.auth.access_token;
                        user.refreshTokenSimpul = responseSimpul.auth.refresh_token;
                        return user;
                    }

                    return null;
                } catch (error) {
                    console.log(error);
                }
            },
        }),
    ],
    secret: process.env.NEXTAUTH_SECRET,
    pages: {
        signIn: '/',
        signOut: '/',
    },
    session: {
        strategy: 'jwt',
        maxAge: 1 * 24 * 60 * 60,
    },
    callbacks: {
        async redirect({ url }) {
            return url;
        },
        async jwt({ token, user, account }: any) {
            if (account?.provider == 'google' && token?.sub != undefined) {
                const resSimpul = await loginSimpulRepository({
                    email: token.email === '<EMAIL>' && process.env.NODE_ENV != 'production' ? '<EMAIL>' : token.email,
                    password: token.email === '<EMAIL>' && process.env.NODE_ENV != 'production' ? 'Password@Paramadina' : token?.sub,
                    scope: token.email === '<EMAIL>' && process.env.NODE_ENV == 'production' ? 'mahasiswa' : 'staff',
                    login_by: token.email === '<EMAIL>' && process.env.NODE_ENV != 'production' ? null : 'google',
                });

                const responseSimpul = resSimpul.data;

                if (!resSimpul.success) {
                    throw new Error('user.message');
                }

                if (resSimpul.success) {
                    return {
                        userSimpul: responseSimpul.user,
                        accessTokenSimpul: responseSimpul.auth.access_token,
                        refreshTokenSimpul: responseSimpul.auth.refresh_token,
                        image: user?.image,
                    };
                }
            }

            if (account && user && account?.provider != 'google') {
                return {
                    ...token,
                    userSimpul: user.userSimpul,
                    accessTokenSimpul: user.accessTokenSimpul,
                    refreshTokenSimpul: user.refreshTokenSimpul,
                    image: user?.image,
                };
            }

            return token;
        },

        async session({ session, token }: any) {
            session.user.image = token?.image;
            session.user.userSimpul = token.userSimpul;
            session.user.accessTokenSimpul = token.accessTokenSimpul;
            session.user.refreshTokenSimpul = token.refreshTokenSimpul;
            session.user.accessTokenExpiresSimpul = token.accessTokenExpiresSimpul;

            return session;
        },
    },
};
