/* Animate css */
@import './animate.css';
/* tippy css */
@import './tippy.css';

/* swiper css */
@import './swiper.css';

/* Elements - Progressbar */
@import './progressbar.css';

/* scrumboard */
@import './scrumboard.css';

/* calendar css */
@import './fullcalendar.css';

/* sweetalert css */
@import './sweetalert.css';

/* flatpickr css */
@import './flatpickr.css';

/* quill-editor css */
@import './quill-editor.css';

/* markdown-editor css */
@import './markdown-editor.css';

/* file upload with preview */
@import './file-upload-preview.css';

/* dragndrop*/
@import './dragndrop.css';

/* form element*/
@import './form-elements.css';

/* Select2*/
@import './select2.css';

@tailwind base;
@tailwind components;
@tailwind utilities;

@layer components {
    html {
        @apply scroll-smooth;
    }

    body {
        @apply bg-[#fafafa] font-nunito;
    }

    body.dark {
        @apply bg-[#060818];
    }

    /* Panel */
    .panel {
        @apply relative p-5 bg-white rounded-md shadow dark:bg-black;
    }
    /* Navbar */
    .navbar-sticky header,
    .navbar-floating header {
        @apply sticky top-0 z-20;
    }
    .navbar-floating header {
        @apply bg-[#fafafa]/90 px-6 pt-4 dark:bg-[#060818]/90;
    }
    .navbar-floating header > div > div:first-child {
        @apply rounded-md;
    }
    .horizontal .navbar-floating header > div > div:first-child {
        @apply rounded-b-none;
    }
    .horizontal .navbar-floating header .horizontal-menu {
        @apply rounded-b-md;
    }

    /* Sidebar */
    .sidebar:hover .nav-item > a {
        @apply w-auto;
    }

    .sidebar .nav-item > button,
    .sidebar .nav-item > a {
        @apply mb-1 flex w-full items-center justify-between overflow-hidden whitespace-nowrap rounded-md p-2.5 text-[#506690] hover:bg-[#000]/[0.08] hover:text-black dark:hover:bg-[#181f32] dark:hover:text-white-dark;
    }
    .sidebar .nav-item > button.active,
    .sidebar .nav-item > a.active {
        @apply bg-[#000]/[0.08] text-black dark:bg-[#181f32] dark:text-white-dark;
    }

    .sidebar .nav-item > button.active > div > span,
    .sidebar .nav-item > a.active > div > span {
        @apply dark:!text-white-dark;
    }

    .sidebar ul.sub-menu li button,
    .sidebar ul.sub-menu li a {
        @apply flex w-full items-center px-9 py-2.5 before:h-0.5 before:w-2 before:rounded before:bg-gray-300 hover:bg-gray-100
hover:text-primary hover:before:!bg-primary ltr:before:mr-2 rtl:before:ml-2 dark:before:bg-gray-500 dark:hover:bg-gray-900 dark:hover:text-primary;
    }
    .sidebar ul.sub-menu li button.active,
    .sidebar ul.sub-menu li a.active {
        @apply text-primary before:bg-primary;
    }

    .sidebar .nav-item a div:first-child svg,
    .sidebar .nav-item button div:first-child svg {
        @apply w-5 h-5 text-black/50 dark:text-white/50;
    }

    .main-container .main-content {
        @apply transition-all duration-300 lg:ltr:ml-[260px] lg:rtl:mr-[260px];
    }

    /* Horizontal layouts */
    .horizontal .horizontal-menu {
        @apply hidden shadow-md lg:flex;
    }
    .horizontal .horizontal-logo {
        @apply flex;
    }
    .horizontal .main-container .main-content {
        @apply ltr:ml-0 rtl:mr-0;
    }
    .horizontal .sidebar {
        @apply ltr:-left-[260px] rtl:-right-[260px];
    }
    .horizontal.toggle-sidebar .sidebar {
        @apply ltr:left-0 rtl:right-0 lg:ltr:-left-[260px] lg:rtl:-right-[260px];
    }

    .horizontal .nav-item a div:first-child svg,
    .horizontal .nav-item button div:first-child svg {
        @apply w-5 h-5 text-black/50 dark:text-white/50;
    }

    .horizontal .dark .nav-item a div:first-child svg,
    .dark.horizontal .nav-item a div:first-child svg,
    .horizontal .dark .nav-item button div:first-child svg,
    .dark.horizontal .nav-item button div:first-child svg {
        @apply text-white/50;
    }

    .horizontal-menu .nav-link {
        @apply flex items-center rounded-lg px-2 py-2.5 hover:bg-[#000]/[0.08] hover:text-black dark:hover:bg-[#181f32] dark:hover:text-white-dark xl:px-4;
    }

    .horizontal-menu .nav-link.active {
        @apply bg-[#000]/[0.08] text-black dark:bg-[#181f32] dark:text-white-dark;
    }

    .horizontal-menu ul.sub-menu {
        @apply absolute top-full z-[10] hidden min-w-[180px] rounded bg-white p-0 py-2 text-dark shadow dark:bg-[#1b2e4b] dark:text-white-dark;
    }

    .horizontal-menu ul.sub-menu a,
    .horizontal-menu ul.sub-menu button {
        @apply flex items-center justify-between w-full px-4 py-2 hover:bg-gray-100 hover:text-primary dark:hover:bg-primary/10;
    }

    .horizontal-menu ul.sub-menu a.active,
    .horizontal-menu ul.sub-menu button.active {
        @apply bg-gray-100 text-primary dark:bg-primary/10;
    }

    .horizontal-menu > li.nav-item:hover > ul.sub-menu,
    .horizontal-menu > li.nav-item > ul.sub-menu > li:hover > ul,
    .horizontal-menu > li.nav-item > ul.sub-menu > li:hover > ul > li:hover > ul {
        @apply block;
    }

    /* Vertical layouts */
    .vertical.toggle-sidebar .horizontal-logo,
    .vertical.toggle-sidebar .collapse-icon {
        @apply flex;
    }
    .vertical.toggle-sidebar .main-container .main-content {
        @apply ltr:ml-0 rtl:mr-0;
    }
    .vertical .sidebar {
        @apply ltr:-left-[260px] rtl:-right-[260px] lg:ltr:left-0 lg:rtl:right-0;
    }
    .vertical.toggle-sidebar .sidebar {
        @apply ltr:left-0 rtl:right-0 lg:ltr:-left-[260px] lg:rtl:-right-[260px];
    }

    /* Collapsible vertical layouts */
    .collapsible-vertical .sidebar {
        @apply hover:w-[260px] ltr:-left-[260px] rtl:-right-[260px] lg:w-[70px] lg:ltr:left-0 lg:rtl:right-0;
    }
    .collapsible-vertical.toggle-sidebar .sidebar {
        @apply ltr:left-0 rtl:right-0;
    }
    .collapsible-vertical.toggle-sidebar .sidebar {
        @apply lg:w-[260px];
    }
    .collapsible-vertical.toggle-sidebar .sidebar .nav-item > a {
        @apply w-auto;
    }
    .collapsible-vertical.toggle-sidebar .main-content {
        @apply lg:w-[calc(100%-260px)] lg:ltr:ml-[260px] lg:rtl:mr-[260px];
    }

    .collapsible-vertical .sidebar .sub-menu {
        @apply lg:hidden;
    }
    .collapsible-vertical .sidebar:hover .sub-menu,
    .collapsible-vertical .sidebar:hover .sub-menu.recent-submenu,
    .collapsible-vertical.toggle-sidebar .sidebar .sub-menu {
        @apply block;
    }
    .collapsible-vertical .main-content {
        @apply lg:w-[calc(100%-70px)] lg:ltr:ml-[70px] lg:rtl:mr-[70px];
    }
    .collapsible-vertical .sidebar .collapse-icon,
    .collapsible-vertical .main-logo > span {
        @apply transition-opacity duration-300 lg:opacity-0;
    }
    .collapsible-vertical .sidebar:hover .collapse-icon,
    .collapsible-vertical.toggle-sidebar .collapse-icon,
    .collapsible-vertical .sidebar:hover .main-logo > span,
    .collapsible-vertical.toggle-sidebar .main-logo > span {
        @apply duration-500 lg:opacity-100;
    }
    .collapsible-vertical.toggle-sidebar .sidebar .collapse-icon {
        @apply flex rotate-0;
    }
    .collapsible-vertical .sidebar:hover .collapse-icon {
        @apply flex rotate-180;
    }
    .collapsible-vertical .sidebar ul > h2 span {
        @apply hidden whitespace-nowrap;
    }
    .collapsible-vertical .sidebar ul > h2 svg {
        @apply block;
    }
    .collapsible-vertical .sidebar:hover ul > h2 span,
    .collapsible-vertical.toggle-sidebar .sidebar ul > h2 span {
        @apply inline;
    }
    .collapsible-vertical .sidebar:hover ul > h2 svg,
    .collapsible-vertical.toggle-sidebar .sidebar ul > h2 svg {
        @apply hidden;
    }

    /* boxed-layout */
    .boxed-layout {
        @apply mx-auto max-w-[1400px];
    }

    .boxed-layout.vertical .sidebar,
    .boxed-layout.collapsible-vertical .sidebar {
        @apply overflow-hidden lg:ltr:left-auto lg:rtl:right-auto;
    }

    .boxed-layout.vertical.toggle-sidebar .sidebar {
        @apply lg:w-0;
    }

    /* Buttons */
    .btn {
        @apply relative flex items-center justify-center rounded-md border px-5 py-2 text-sm font-semibold shadow-[0_10px_20px_-10px] outline-none transition duration-300 hover:shadow-none;
    }
    .btn-lg {
        @apply px-7 py-2.5 text-base;
    }
    .btn-sm {
        @apply px-2.5 py-1.5 text-xs;
    }
    .btn[disabled] {
        @apply cursor-not-allowed opacity-60;
    }

    .btn-primary {
        @apply text-white border-primary bg-primary shadow-primary/60;
    }
    .btn-outline-primary {
        @apply shadow-none border-primary text-primary hover:bg-primary hover:text-white;
    }

    .btn-secondary {
        @apply text-white border-secondary bg-secondary shadow-secondary/60;
    }
    .btn-outline-secondary {
        @apply shadow-none border-secondary text-secondary hover:bg-secondary hover:text-white;
    }

    .btn-success {
        @apply text-white border-success bg-success shadow-success/60;
    }
    .btn-outline-success {
        @apply shadow-none border-success text-success hover:bg-success hover:text-white;
    }

    .btn-danger {
        @apply text-white border-danger bg-danger shadow-danger/60;
    }
    .btn-outline-danger {
        @apply shadow-none border-danger text-danger hover:bg-danger hover:text-white;
    }

    .btn-warning {
        @apply text-white border-warning bg-warning shadow-warning/60;
    }
    .btn-outline-warning {
        @apply shadow-none border-warning text-warning hover:bg-warning hover:text-white;
    }

    .btn-info {
        @apply text-white border-info bg-info shadow-info/60;
    }
    .btn-outline-info {
        @apply shadow-none border-info text-info hover:bg-info hover:text-white;
    }

    .btn-dark {
        @apply text-white border-dark bg-dark shadow-dark/60;
    }

    .btn-outline-dark {
        @apply shadow-none border-dark text-dark hover:bg-dark hover:text-white;
    }

    .btn-gradient {
        @apply bg-gradient-to-r from-[#EF1262] to-[#4361EE] text-white hover:from-[#4361EE] hover:to-[#EF1262];
    }

    /* Badge */
    .badge {
        @apply relative my-1 rounded border border-transparent px-2 py-0.5 text-xs font-semibold text-white;
    }
    .badge-outline-primary {
        @apply border-primary text-primary hover:bg-primary-light dark:hover:bg-primary dark:hover:text-white-light;
    }
    .badge-outline-secondary {
        @apply border-secondary text-secondary hover:bg-secondary-light dark:hover:bg-secondary dark:hover:text-white-light;
    }
    .badge-outline-success {
        @apply border-success text-success hover:bg-success-light dark:hover:bg-success dark:hover:text-white-light;
    }
    .badge-outline-danger {
        @apply border-danger text-danger hover:bg-danger-light dark:hover:bg-danger dark:hover:text-white-light;
    }
    .badge-outline-warning {
        @apply border-warning text-warning hover:bg-warning-light dark:hover:bg-warning dark:hover:text-white-light;
    }
    .badge-outline-info {
        @apply border-info text-info hover:bg-info-light dark:hover:bg-info dark:hover:text-white-light;
    }
    .badge-outline-dark {
        @apply border-dark text-dark hover:bg-dark-light dark:hover:bg-dark dark:hover:text-white-light;
    }

    /* Form */
    .form-input,
    .form-textarea,
    .form-select,
    .form-multiselect {
        @apply w-full rounded-md border border-white-light bg-white px-4 py-2 text-sm font-semibold text-black !outline-none focus:border-primary focus:ring-transparent dark:border-[#17263c] dark:bg-[#121e32] dark:text-white-dark dark:focus:border-primary;
    }

    .form-input-lg,
    .form-textarea-lg,
    .form-select-lg,
    .form-multiselect-lg {
        @apply py-2.5 text-base;
    }
    .form-input-sm,
    .form-textarea-sm,
    .form-select-sm,
    .form-multiselect-sm {
        @apply py-1.5 text-xs;
    }
    label {
        @apply mb-1.5 block font-semibold;
    }
    [dir='rtl'] select {
        background-position: left 0.5rem center;
    }

    .has-error .form-input,
    .has-error .form-textarea,
    .has-error .form-select,
    .has-error .form-multiselect,
    .has-error .multiselect__tags {
        @apply border-danger bg-danger/[0.08] text-danger placeholder-danger/70 focus:border-danger;
    }
    .has-error .form-label,
    .has-error .form-help,
    .has-error .form-icon,
    .has-error .multiselect__placeholder {
        @apply text-danger;
    }
    .has-error .multiselect__input {
        @apply bg-[#F7ECF0] !placeholder-danger/70;
    }
    .has-error .multiselect__tags:hover,
    .has-error .form-checkbox {
        @apply border-danger;
    }

    .has-success .form-input,
    .has-success .form-textarea,
    .has-success .form-select,
    .has-success .form-multiselect,
    .has-success .multiselect__tags {
        @apply border-success bg-success/[0.08] text-success placeholder-success/70 focus:border-success;
    }
    .has-success .form-label,
    .has-success .form-help,
    .has-success .form-icon,
    .has-success .multiselect__placeholder {
        @apply text-success;
    }
    .has-success .multiselect__input {
        @apply bg-[#F7ECF0] !placeholder-success/70;
    }
    .has-success .multiselect__tags:hover {
        @apply border-success;
    }

    /* checkbox & radio */
    .form-radio,
    .form-checkbox {
        @apply h-5 w-5 cursor-pointer rounded border-2 border-white-light bg-transparent text-primary !shadow-none !outline-none !ring-0 !ring-offset-0 checked:bg-[length:90%_90%] disabled:cursor-not-allowed disabled:bg-[#eee] ltr:mr-1.5 rtl:ml-1.5
      dark:border-[#253b5c] dark:checked:border-transparent dark:disabled:bg-[#1b2e4b];
    }

    .form-checkbox.outline-primary:checked {
        @apply bg-transparent border-primary;
        background-image: url("data:image/svg+xml,<svg viewBox='0 0 16 16' fill='%234361ee' xmlns='http://www.w3.org/2000/svg'><path d='M12.207 4.793a1 1 0 010 1.414l-5 5a1 1 0 01-1.414 0l-2-2a1 1 0 011.414-1.414L6.5 9.086l4.293-4.293a1 1 0 011.414 0z'/></svg>");
    }
    .form-checkbox.outline-secondary:checked {
        @apply bg-transparent border-secondary;
        background-image: url("data:image/svg+xml,<svg viewBox='0 0 16 16' fill='%23805dca' xmlns='http://www.w3.org/2000/svg'><path d='M12.207 4.793a1 1 0 010 1.414l-5 5a1 1 0 01-1.414 0l-2-2a1 1 0 011.414-1.414L6.5 9.086l4.293-4.293a1 1 0 011.414 0z'/></svg>");
    }
    .form-checkbox.outline-success:checked {
        @apply bg-transparent border-success;
        background-image: url("data:image/svg+xml,<svg viewBox='0 0 16 16' fill='%2300ab55' xmlns='http://www.w3.org/2000/svg'><path d='M12.207 4.793a1 1 0 010 1.414l-5 5a1 1 0 01-1.414 0l-2-2a1 1 0 011.414-1.414L6.5 9.086l4.293-4.293a1 1 0 011.414 0z'/></svg>");
    }
    .form-checkbox.outline-danger:checked {
        @apply bg-transparent border-danger;
        background-image: url("data:image/svg+xml,<svg viewBox='0 0 16 16' fill='%23e7515a' xmlns='http://www.w3.org/2000/svg'><path d='M12.207 4.793a1 1 0 010 1.414l-5 5a1 1 0 01-1.414 0l-2-2a1 1 0 011.414-1.414L6.5 9.086l4.293-4.293a1 1 0 011.414 0z'/></svg>");
    }
    .form-checkbox.outline-warning:checked {
        @apply bg-transparent border-warning;
        background-image: url("data:image/svg+xml,<svg viewBox='0 0 16 16' fill='%23e2a03f' xmlns='http://www.w3.org/2000/svg'><path d='M12.207 4.793a1 1 0 010 1.414l-5 5a1 1 0 01-1.414 0l-2-2a1 1 0 011.414-1.414L6.5 9.086l4.293-4.293a1 1 0 011.414 0z'/></svg>");
    }
    .form-checkbox.outline-info:checked {
        @apply bg-transparent border-info;
        background-image: url("data:image/svg+xml,<svg viewBox='0 0 16 16' fill='%232196f3' xmlns='http://www.w3.org/2000/svg'><path d='M12.207 4.793a1 1 0 010 1.414l-5 5a1 1 0 01-1.414 0l-2-2a1 1 0 011.414-1.414L6.5 9.086l4.293-4.293a1 1 0 011.414 0z'/></svg>");
    }
    .form-checkbox.outline-dark:checked {
        @apply bg-transparent border-dark;
        background-image: url("data:image/svg+xml,<svg viewBox='0 0 16 16' fill='%233b3f5c' xmlns='http://www.w3.org/2000/svg'><path d='M12.207 4.793a1 1 0 010 1.414l-5 5a1 1 0 01-1.414 0l-2-2a1 1 0 011.414-1.414L6.5 9.086l4.293-4.293a1 1 0 011.414 0z'/></svg>");
    }

    .form-radio {
        @apply rounded-full;
    }

    .form-radio.outline-primary:checked {
        @apply bg-transparent border-primary bg-none;
    }
    .form-radio.outline-secondary:checked {
        @apply bg-transparent border-secondary bg-none;
    }
    .form-radio.outline-success:checked {
        @apply bg-transparent border-success bg-none;
    }
    .form-radio.outline-danger:checked {
        @apply bg-transparent border-danger bg-none;
    }
    .form-radio.outline-warning:checked {
        @apply bg-transparent border-warning bg-none;
    }
    .form-radio.outline-info:checked {
        @apply bg-transparent border-info bg-none;
    }
    .form-radio.outline-dark:checked {
        @apply bg-transparent border-dark bg-none;
    }

    /* dropdown */
    .dropdown {
        @apply relative;
    }
    .dropdown > button {
        @apply flex;
    }
    .dropdown ul {
        @apply my-1 min-w-[120px] rounded bg-white p-0 py-2 text-black shadow dark:bg-[#1b2e4b] dark:text-white-dark;
    }
    .dropdown ul li > a,
    .dropdown ul li > button {
        @apply flex items-center px-4 py-2 hover:bg-primary/10 hover:text-primary;
    }
    .dropdown ul li > button {
        @apply w-full;
    }

    /* tables */
    .table-responsive {
        @apply overflow-auto;
    }
    table {
        @apply w-full !border-collapse;
    }
    table thead tr th,
    table tfoot tr th,
    table tbody tr td {
        @apply px-4 py-3 ltr:text-left rtl:text-right;
    }
    table thead tr th,
    table tfoot tr th {
        @apply font-semibold;
    }
    table tbody tr {
        @apply border-b !border-white-light/40 dark:!border-[#191e3a];
    }
    table.table-hover tbody tr {
        @apply hover:!bg-white-light/20 dark:hover:!bg-[#1a2941]/40;
    }
    table.table-striped tbody tr:nth-child(odd) {
        @apply !bg-white-light/20 dark:!bg-[#1a2941]/40;
    }

    table.dataTable-table tbody tr th,
    table.dataTable-table tbody tr td {
        @apply border-b border-white-light/40 px-4 py-3 ltr:text-left rtl:text-right dark:border-[#191e3a];
    }
    table.dataTable-table tbody tr:last-child td {
        @apply border-b-0;
    }

    /* code hightlight */
    pre {
        direction: ltr;
    }
}

/* perfect scrollbar */
.ps__rail-y > .ps__thumb-y,
.ps__rail-y > .ps__thumb-y {
    @apply !w-1.5 !bg-[#DDDDDD] dark:!bg-[#2d334c];
}
.ps .ps__rail-x:hover,
.ps .ps__rail-y:hover,
.ps .ps__rail-x:focus,
.ps .ps__rail-y:focus,
.ps .ps__rail-x.ps--clicking,
.ps .ps__rail-y.ps--clicking {
    @apply !opacity-60;
}
.ps .ps__rail-x:hover,
.ps .ps__rail-y:hover,
.ps .ps__rail-x:focus,
.ps .ps__rail-y:focus,
.ps .ps__rail-x.ps--clicking,
.ps .ps__rail-y.ps--clicking {
    @apply !bg-transparent;
}

/* Animations */
.slide-down-enter-active {
    @apply transition duration-100 ease-out;
}
.slide-down-leave-active {
    @apply transition duration-75 ease-in;
}
.slide-down-enter-from,
.slide-down-leave-to {
    @apply transform scale-95 opacity-0;
}
.slide-down-enter-to,
.slide-down-leave-from {
    @apply transform scale-100 opacity-100;
}

.modal-fade-enter-active {
    @apply transition duration-300 ease-out;
}
.modal-fade-leave-active {
    @apply transition duration-200 ease-in;
}
.modal-fade-enter-from,
.modal-fade-leave-to {
    @apply transform scale-95 opacity-0;
}
.modal-fade-enter-to,
.modal-fade-leave-from {
    @apply transform scale-100 opacity-100;
}

/* Hightlight JS */
pre.hljs {
    @apply overflow-x-auto rounded-md !bg-[#191e3a] p-6;
}

/* apex chart */
.apexcharts-canvas .apexcharts-tooltip.apexcharts-theme-light,
.apexcharts-canvas .apexcharts-xaxistooltip.apexcharts-theme-light {
    box-shadow: none;
    @apply border-[#050717cc] bg-[#050717cc] text-white;
}

.apexcharts-canvas .apexcharts-xaxistooltip-bottom:before,
.apexcharts-canvas .apexcharts-xaxistooltip-bottom:after {
    @apply border-b-[#050717cc];
}

.apexcharts-canvas .apexcharts-tooltip-series-group.apexcharts-active {
    @apply text-white;
}

.apexcharts-canvas .apexcharts-tooltip.apexcharts-theme-light .apexcharts-tooltip-title {
    @apply border-dark bg-[#060818];
}

.apexcharts-legend-series {
    @apply ltr:!mr-2 rtl:!ml-2;
}

.dark .apexcharts-title-text {
    fill: #e0e6ed;
}

.dark .apexcharts-canvas .apexcharts-text.apexcharts-xaxis-label,
.dark .apexcharts-canvas .apexcharts-text.apexcharts-yaxis-label {
    fill: #e0e6ed;
}

.dark .apexcharts-canvas .apexcharts-text,
.dark .apexcharts-canvas .apexcharts-text {
    fill: #e0e6ed;
}

.dark .apexcharts-canvas .apexcharts-legend-text {
    color: #e0e6ed !important;
}

.dark .apexcharts-canvas .apexcharts-radialbar-track.apexcharts-track .apexcharts-radialbar-area {
    stroke: #191e3a;
}
.dark .apexcharts-canvas .apexcharts-series-markers.apexcharts-series-bubble .apexcharts-marker {
    stroke: #191e3a;
}

.dark .apexcharts-canvas .apexcharts-pie-label,
.dark .apexcharts-canvas .apexcharts-datalabel,
.dark .apexcharts-canvas .apexcharts-datalabel-label,
.dark .apexcharts-canvas .apexcharts-datalabel-value {
    fill: #bfc9d4;
}

.dark .apexcharts-canvas .apexcharts-tooltip.apexcharts-theme-dark {
    box-shadow: none;
}

.apexcharts-canvas .apexcharts-legend-marker {
    @apply ltr:!mr-1.5 rtl:!mr-0 rtl:ml-1.5;
}

[dir='rtl'] .apexcharts-tooltip-marker {
    @apply ml-2.5 mr-0;
}

/* swal2 */
.swal2-container .swal2-close {
    @apply text-white hover:text-dark-light focus:shadow-none;
}

.swal2-container .swal2-popup.swal2-toast {
    @apply bg-dark px-5 py-2.5;
}

.swal2-popup.swal2-toast .swal2-title,
.swal2-container .swal2-popup.swal2-toast .swal2-html-container {
    @apply text-black;
}
.swal2-container .swal2-popup.swal2-toast.color-primary {
    @apply bg-primary;
}

.swal2-container .swal2-popup.swal2-toast.color-secondary {
    @apply bg-secondary;
}

.swal2-container .swal2-popup.swal2-toast.color-warning {
    @apply bg-warning;
}
.swal2-container .swal2-popup.swal2-toast.color-info {
    @apply bg-info;
}
.swal2-container .swal2-popup.swal2-toast.color-danger {
    @apply bg-danger;
}
.swal2-container .swal2-popup.swal2-toast.color-success {
    @apply bg-success;
}

img.dark-img {
    @apply hidden;
}
.dark img.light-img {
    @apply !hidden;
}
.dark img.dark-img {
    @apply !block;
}


.text-sip {
    color: #166395;
}

.bg-sip {
    background-color: #166395;
}

.border-sip {
    border-color: #166395;
}

.text-sip-drop {
    color: #dbe9fa;
}

.bg-sip-drop {
    background-color: #dbe9fa;
}

.border-sip-drop {
    border-color: #dbe9fa;
}

.text-sip-light {
    color: #5b9be9;
}

.bg-sip-light {
    background-color: #5b9be9;
}

.border-sip-light {
    border-color: #5b9be9;
}

.collapsible-vertical .sidebar .vertical-logo {
    @apply block;
}

.collapsible-vertical .sidebar .horizontal-logo {
    @apply hidden whitespace-nowrap;
}

.collapsible-vertical .sidebar:hover .vertical-logo {
    @apply hidden;
}

.collapsible-vertical .sidebar:hover .horizontal-logo {
    @apply inline;
}
 
#app {
    width: 100vw;
    height: 100vh;
}

.react-flow__node-selectorNode {
    font-size: 12px;
    background: #eee;
    border: 1px solid #555;
    border-radius: 5px;
    text-align: center;
}


/*********************************/
/*         Testimonial           */
/*===============================*/
.tns-nav{
    margin-top: 0.75rem;
    text-align: center;
  }
  .tns-nav button{
    margin: 0.25rem;
    border-radius: 3px;
    border-width: 0px;
    background-color: rgb(79 70 229 / 0.3);
    padding: 5px;
    -webkit-transition-duration: 500ms;
            transition-duration: 500ms;
  }
  .tns-nav button.tns-nav-active{
    --tw-rotate: 45deg;
    -webkit-transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
            transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
    --tw-bg-opacity: 1;
    background-color: rgb(79 70 229 / var(--tw-bg-opacity, 1));
  }
  
  /* Tns control */
  .tns-controls button[data-controls=prev],
  .tns-controls button[data-controls=next]{
    position: absolute;
    top: 50%;
    z-index: 10;
    width: 2rem;
    height: 2rem;
    --tw-translate-y: -50%;
    -webkit-transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
            transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
    border-radius: 9999px;
    border-width: 0px;
    --tw-bg-opacity: 1;
    background-color: rgb(255 255 255 / var(--tw-bg-opacity, 1));
    font-size: 1rem;
    line-height: 1.5rem;
    --tw-text-opacity: 1;
    color: rgb(60 72 88 / var(--tw-text-opacity, 1));
    --tw-shadow: 0 5px 13px rgb(60 72 88 / 0.20);
    --tw-shadow-colored: 0 5px 13px var(--tw-shadow-color);
    -webkit-box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow);
            box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow);
    -webkit-transition-duration: 500ms;
            transition-duration: 500ms;
  }
  .tns-controls button[data-controls=prev]:is(.dark *),
  .tns-controls button[data-controls=next]:is(.dark *){
    --tw-bg-opacity: 1;
    background-color: rgb(15 23 42 / var(--tw-bg-opacity, 1));
    --tw-text-opacity: 1;
    color: rgb(255 255 255 / var(--tw-text-opacity, 1));
    --tw-shadow-color: #1f2937;
    --tw-shadow: var(--tw-shadow-colored);
  }
  .tns-controls button[data-controls=prev]:hover,
  .tns-controls button[data-controls=next]:hover{
    --tw-bg-opacity: 1;
    background-color: rgb(79 70 229 / var(--tw-bg-opacity, 1));
    --tw-text-opacity: 1;
    color: rgb(255 255 255 / var(--tw-text-opacity, 1));
  }
  .tns-controls button[data-controls=prev]{
    inset-inline-start: 0px;
  }
  .tns-controls button[data-controls=next]{
    inset-inline-end: 0px;
  }
  
  @-webkit-keyframes scroll {
    0% {
      -webkit-transform: translateX(0);
              transform: translateX(0);
    }
    100% {
      -webkit-transform: translateX(-2160px);
              transform: translateX(-2160px);
    }
  }
  
  @keyframes scroll {
    0% {
      -webkit-transform: translateX(0);
              transform: translateX(0);
    }
    100% {
      -webkit-transform: translateX(-2160px);
              transform: translateX(-2160px);
    }
  }
  .slider:after {
    -webkit-transform: rotateZ(360deg);
            transform: rotateZ(360deg);
  }
  .slider .slide-track {
    -webkit-animation: scroll 120s linear infinite;
            animation: scroll 120s linear infinite;
    width: 7200px;
  }


.backdrop-opacity-30{
    --tw-backdrop-opacity: opacity(0.3) !important;
    -webkit-backdrop-filter: var(--tw-backdrop-blur) var(--tw-backdrop-brightness) var(--tw-backdrop-contrast) var(--tw-backdrop-grayscale) var(--tw-backdrop-hue-rotate) var(--tw-backdrop-invert) var(--tw-backdrop-opacity) var(--tw-backdrop-saturate) var(--tw-backdrop-sepia) !important;
    backdrop-filter: var(--tw-backdrop-blur) var(--tw-backdrop-brightness) var(--tw-backdrop-contrast) var(--tw-backdrop-grayscale) var(--tw-backdrop-hue-rotate) var(--tw-backdrop-invert) var(--tw-backdrop-opacity) var(--tw-backdrop-saturate) var(--tw-backdrop-sepia) !important;
  }
  .transition{
    -webkit-transition-property: color, background-color, border-color, text-decoration-color, fill, stroke, opacity, -webkit-box-shadow, -webkit-transform, -webkit-filter, -webkit-backdrop-filter !important;
    transition-property: color, background-color, border-color, text-decoration-color, fill, stroke, opacity, -webkit-box-shadow, -webkit-transform, -webkit-filter, -webkit-backdrop-filter !important;
    transition-property: color, background-color, border-color, text-decoration-color, fill, stroke, opacity, box-shadow, transform, filter, backdrop-filter !important;
    transition-property: color, background-color, border-color, text-decoration-color, fill, stroke, opacity, box-shadow, transform, filter, backdrop-filter, -webkit-box-shadow, -webkit-transform, -webkit-filter, -webkit-backdrop-filter !important;
    -webkit-transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1) !important;
            transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1) !important;
    -webkit-transition-duration: 150ms !important;
            transition-duration: 150ms !important;
  }
  .transition-all{
    -webkit-transition-property: all !important;
    transition-property: all !important;
    -webkit-transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1) !important;
            transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1) !important;
    -webkit-transition-duration: 150ms !important;
            transition-duration: 150ms !important;
  }
  .duration-300{
    -webkit-transition-duration: 300ms !important;
            transition-duration: 300ms !important;
  }
  .duration-500{
    -webkit-transition-duration: 500ms !important;
            transition-duration: 500ms !important;
  }
  .duration-700{
    -webkit-transition-duration: 700ms !important;
            transition-duration: 700ms !important;
  }
  .ease-in{
    -webkit-transition-timing-function: cubic-bezier(0.4, 0, 1, 1) !important;
            transition-timing-function: cubic-bezier(0.4, 0, 1, 1) !important;
  }
  .ease-in-out{
    -webkit-transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1) !important;
            transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1) !important;
  }

/* Kenburn Effect for Business Demo */
.image-wrap {
    -webkit-animation: 100s ppb_kenburns linear infinite alternate;
            animation: 100s ppb_kenburns linear infinite alternate;
  }
  
  @-webkit-keyframes move {
    0% {
      -webkit-transform-origin: bottom;
              transform-origin: bottom;
      -webkit-transform: scale(1);
              transform: scale(1);
    }
    100% {
      -webkit-transform: scale(1.4);
              transform: scale(1.4);
    }
  }
  
  @keyframes move {
    0% {
      -webkit-transform-origin: bottom;
              transform-origin: bottom;
      -webkit-transform: scale(1);
              transform: scale(1);
    }
    100% {
      -webkit-transform: scale(1.4);
              transform: scale(1.4);
    }
  }
  @-webkit-keyframes ppb_kenburns {
    0% {
      -webkit-transform: scale(1.3) translate(-10%, 10%);
              transform: scale(1.3) translate(-10%, 10%);
    }
    25% {
      -webkit-transform: scale(1) translate(0, 0);
              transform: scale(1) translate(0, 0);
    }
    50% {
      -webkit-transform: scale(1.3) translate(10%, 10%);
              transform: scale(1.3) translate(10%, 10%);
    }
    75% {
      -webkit-transform: scale(1) translate(0, 0);
              transform: scale(1) translate(0, 0);
    }
    100% {
      -webkit-transform: scale(1.3) translate(-10%, 10%);
              transform: scale(1.3) translate(-10%, 10%);
    }
  }
  @keyframes ppb_kenburns {
    0% {
      -webkit-transform: scale(1.3) translate(-10%, 10%);
              transform: scale(1.3) translate(-10%, 10%);
    }
    25% {
      -webkit-transform: scale(1) translate(0, 0);
              transform: scale(1) translate(0, 0);
    }
    50% {
      -webkit-transform: scale(1.3) translate(10%, 10%);
              transform: scale(1.3) translate(10%, 10%);
    }
    75% {
      -webkit-transform: scale(1) translate(0, 0);
              transform: scale(1) translate(0, 0);
    }
    100% {
      -webkit-transform: scale(1.3) translate(-10%, 10%);
              transform: scale(1.3) translate(-10%, 10%);
    }
  }
  .spa-css{
    font-family: "Work Sans", sans-serif !important;
  }
  .spa-css #topnav .navigation-menu > li > a{
    padding-left: 12px !important;
    padding-right: 12px !important;
    font-size: 14px !important;
    font-weight: 500 !important;
  }
  .spa-css #topnav .navigation-menu > li .submenu li a{
    font-size: 12px !important;
    font-weight: 500 !important;
  }
  .spa-css #topnav .navigation-menu > li .submenu.megamenu li .megamenu-head{
    font-weight: 500 !important;
  }

  /* Styling tambahan untuk memastikan dots berada di dalam carousel */
.owl-carousel .owl-dots {
    position: absolute;
    bottom: 20px; /* Menyesuaikan posisi dots agar berada di bawah konten */
    left: 50%;
    transform: translateX(-50%);
    z-index: 10; /* Agar dots tetap di atas konten */
}

.container-filter-white li.active, .container-filter-white li:hover{
    --tw-border-opacity: 1 !important;
    border-bottom-color: rgb(255 255 255 / var(--tw-border-opacity, 1)) !important;
    --tw-text-opacity: 1 !important;
    color: rgb(255 255 255 / var(--tw-text-opacity, 1)) !important;
  }

  /* Pastikan scrollbar horizontal terlihat selalu */
.scrollbar-always-visible {
    overflow-x: scroll;
    scrollbar-width: auto;
  }
  
  /* Chrome, Edge, Safari */
  .scrollbar-always-visible::-webkit-scrollbar {
    height: 8px;
  }
  
  .scrollbar-always-visible::-webkit-scrollbar-thumb {
    background-color: #c0c0c0; /* warna thumb */
    border-radius: 4px;
  }
  