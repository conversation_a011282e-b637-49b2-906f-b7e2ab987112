import { isHaveValue } from '@helpers/isHaveValue';
import { getDataFromStorage, setDataToStorage, useCheckCurrentCache } from '@helpers/setOptionStorage';
import toUpperCaseAllWord from '@helpers/toUpperCaseAllWord';
import { authResponseAccessTokenSimpulSelector } from '@reduxThunk/accounts/auth/selector';
import { DataMasterKodePos, MasterKodePosResponseModel } from '@restApi/hris/dataMaster/masterKodePos/models';
import { getAllListMasterKodePosRepository } from '@restApi/hris/dataMaster/masterKodePos/repository';
import { initialFilter } from '@static/initial-state';
import { useEffect, useState } from 'react';
import { useSelector } from 'react-redux';

export const useLovMasterKodePos = (master_subdistrict_id?: string, master_province_id?: string) => {
    const token = useSelector(authResponseAccessTokenSimpulSelector);
    const initFilter = JSON.parse(JSON.stringify({ ...initialFilter }));
    const filter = {
        ...initFilter,
        primary_key: 'postal_id',
        order_key: 'postal_id',
        limit: 5000,
        filters: master_subdistrict_id && master_province_id
            ? [
                  {
                      column: 'master_subdistrict_id',
                      equal: true,
                      values: [master_subdistrict_id],
                  },
                  {
                      column: 'master_province_id',
                      equal: true,
                      values: [master_province_id],
                  },
              ]
            : [],
    };
    const filterCache = {
        ...initFilter,
        limit: 5000,
        filters: [],
    };
    const [loading, setLoading] = useState(true);
    const [options, setOptions] = useState<
        {
            label: string;
            value: DataMasterKodePos;
        }[]
    >([]);

    const { isSameData } = useCheckCurrentCache(token, filterCache, 'master_kode_pos');

    async function getDataLov() {
        if (!master_subdistrict_id) {
            setOptions([]);
            return;
        }

        try {
            setLoading(true);
            const nameStorage = `master_kode_pos`;
            const data: any = await getDataFromStorage(nameStorage, token, filterCache);
            let options: any = data?.options ?? [];

            if (!isSameData || !isHaveValue(options)) {
                const response: MasterKodePosResponseModel = await getAllListMasterKodePosRepository(token, filter);
                const listData = response?.data ?? [];
                options = listData
                    .map((item: DataMasterKodePos) => ({
                        label: item?.postal_code.toString(),
                        value: item,
                    }))
                    .sort((a, b) => {
                        return a.label.localeCompare(b.label);
                    });
                setDataToStorage(nameStorage, options, token, filterCache);
            }

            setOptions(options);
            setLoading(false);
        } catch (_) {
            setOptions([]);
            setLoading(false);
        }
    }

    useEffect(() => {
        getDataLov();
        // eslint-disable-next-line react-hooks/exhaustive-deps
    }, [master_subdistrict_id, isSameData]);

    return { options, loading };
};
