import { isHaveValue } from '@helpers/isHaveValue';
import { getDataFromStorage, setDataToStorage, useCheckCurrentCache } from '@helpers/setOptionStorage';
import toUpperCaseAllWord from '@helpers/toUpperCaseAllWord';
import { authResponseAccessTokenSimpulSelector } from '@reduxThunk/accounts/auth/selector';

import { DataMasterProgramStudiSevima } from '@restApi/hris/dataMaster/masterProgramStudiSevima/models';
import { DataMasterPeriodeSevima, MasterPeriodeSevimaResponseModel } from '@restApi/sevima/masterPeriodeSevima/models';
import { getAllListMasterPeriodeSevimaRepository } from '@restApi/sevima/masterPeriodeSevima/repository';

import { initialFilter } from '@static/initial-state';
import { useEffect, useState } from 'react';
import { useSelector } from 'react-redux';

export const useLovMasterPeriodeSevima = () => {
    const token = useSelector(authResponseAccessTokenSimpulSelector);
    const initFilter = JSON.parse(JSON.stringify({ ...initialFilter }));
    const filter = {
        ...initFilter,
        limit: 5000,
    };
    const filterCache = {
        ...initFilter,
        limit: 5000,
        filters: [],
    };
    const [loading, setLoading] = useState(true);
    const [options, setOptions] = useState<
        {
            label: string;
            value: DataMasterPeriodeSevima;
        }[]
    >([]);

    const { isSameData } = useCheckCurrentCache(token, filterCache, 'master_periode_sevima');

    async function getDataLov() {
        try {
            setLoading(true);
            const nameStorage = 'master_periode_sevima';
            const data: any = await getDataFromStorage(nameStorage, token, filterCache);
            let options: any = data?.options ?? [];

            if (!isSameData || !isHaveValue(options)) {
                const response: MasterPeriodeSevimaResponseModel = await getAllListMasterPeriodeSevimaRepository(token, filter);
                const listData = response?.data ?? [];
            options = listData
                .map((item: DataMasterPeriodeSevima) => {
                    return {
                        label: toUpperCaseAllWord(item?.nama_periode || ''),
                        value: item,
                    };
                })
                .sort((a, b) => (b.value.tahun_ajar || '').localeCompare(a.value.tahun_ajar || ''));
                setDataToStorage(nameStorage, options, token, filterCache);
            }

            setOptions(options);
            setLoading(false);
        } catch (_) {
            setOptions([]);
            setLoading(false);
        }
    }

    useEffect(() => {
        getDataLov();
        // eslint-disable-next-line react-hooks/exhaustive-deps
    }, [isSameData]);

    return { options, loading };
};
