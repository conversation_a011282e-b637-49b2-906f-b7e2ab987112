import { isHaveValue } from '@helpers/isHaveValue';
import { getDataFromStorage, setDataToStorage, useCheckCurrentCache } from '@helpers/setOptionStorage';
import toUpperCaseAllWord from '@helpers/toUpperCaseAllWord';
import { authResponseAccessTokenSimpulSelector } from '@reduxThunk/accounts/auth/selector';
import { DataMasterProgramStudiSevima, MasterProgramStudiSevimaResponseModel } from '@restApi/hris/dataMaster/masterProgramStudiSevima/models';
import { getAllListMasterProgramStudiSevimaRepository } from '@restApi/hris/dataMaster/masterProgramStudiSevima/repository';
import { initialFilter } from '@static/initial-state';
import { useEffect, useState } from 'react';
import { useSelector } from 'react-redux';

export const useLovMasterProgramStudiSevima = () => {
    const token = useSelector(authResponseAccessTokenSimpulSelector);
    const initFilter = JSON.parse(JSON.stringify({ ...initialFilter }));
    const filter = {
        ...initFilter,
        limit: 5000,
        filters: [
            {
                column: 'id_jenjang',
                values: ['S1'],
                equal: true,
            },
        ],
    };
    const filterCache = {
        ...initFilter,
        limit: 5000,
        filters: [],
    };
    const [loading, setLoading] = useState(true);
    const [options, setOptions] = useState<
        {
            label: string;
            value: DataMasterProgramStudiSevima;
        }[]
    >([]);

    const { isSameData } = useCheckCurrentCache(token, filterCache, 'master_program_studi_sevima');

    async function getDataLov() {
        try {
            setLoading(true);
            const nameStorage = 'master_program_studi_sevima';
            const data: any = await getDataFromStorage(nameStorage, token, filterCache);
            let options: any = data?.options ?? [];

            if (!isSameData || !isHaveValue(options)) {
                const response: MasterProgramStudiSevimaResponseModel = await getAllListMasterProgramStudiSevimaRepository(token, filter);
                const listData = response?.data ?? [];

            options = listData
                .map((item: DataMasterProgramStudiSevima) => {
                    // Hilangkan prefix "S1 - " jika ada
                    let nama = item?.nama_program_studi || '';
                    if (nama.includes('-')) {
                        nama = nama.split('-').slice(1).join('-').trim(); 
                    }

                    return {
                        label: toUpperCaseAllWord(nama),
                        value: item,
                    };
                })
                .sort((a, b) => a.label.localeCompare(b.label));
                setDataToStorage(nameStorage, options, token, filterCache);
            }

            setOptions(options);
            setLoading(false);
        } catch (_) {
            setOptions([]);
            setLoading(false);
        }
    }

    useEffect(() => {
        getDataLov();
        // eslint-disable-next-line react-hooks/exhaustive-deps
    }, [isSameData]);

    return { options, loading };
};
