import { isHaveValue } from '@helpers/isHaveValue';
import { getDataFromStorage, setDataToStorage, useCheckCurrentCache } from '@helpers/setOptionStorage';
import toUpperCaseAllWord from '@helpers/toUpperCaseAllWord';
import { authResponseAccessTokenSimpulSelector } from '@reduxThunk/accounts/auth/selector';
import { DataMasterJenisCuti, MasterJenisCutiResponseModel } from '@restApi/hris/dataMaster/masterJenisCuti/models';
import { getAllListMasterJenisCutiRepository } from '@restApi/hris/dataMaster/masterJenisCuti/repository';
import { DataMasterSemester, MasterSemesterResponseModel } from '@restApi/hris/dataMaster/masterSemester/models';
import { getAllListMasterSemesterRepository } from '@restApi/hris/dataMaster/masterSemester/repository';
import { initialFilter } from '@static/initial-state';
import { useEffect, useState } from 'react';
import { useSelector } from 'react-redux';

export const useLovMasterSemester = () => {
    const token = useSelector(authResponseAccessTokenSimpulSelector);
    const initFilter = JSON.parse(JSON.stringify({ ...initialFilter }));
    const filter = { ...initFilter, limit: 5000 };
    const filterCache = { 
        ...initFilter,
        limit: 5000,
        filters: [],
    };
    const [loading, setLoading] = useState(true);
    const [options, setOptions] = useState<
        {
            label: string;
            value: DataMasterSemester;
        }[]
    >([]);

    const { isSameData } = useCheckCurrentCache(token, filterCache, 'master_semester');

    async function getDataLov() {
        try {
            setLoading(true);
            const nameStorage = 'master_semester';
            const data: any = await getDataFromStorage(nameStorage, token, filterCache);
            let options: any = data?.options ?? [];

            if (!isSameData || !isHaveValue(options)) {
                const response: MasterSemesterResponseModel = await getAllListMasterSemesterRepository(token, filter);
                const listData = response?.data ?? [];
                options = listData
                    .map((item: DataMasterSemester) => ({
                        label: toUpperCaseAllWord(item?.nama_periode),
                        value: item,
                    }))
                    .sort((a, b) => {
                        return a.label.localeCompare(b.label);
                    });
                setDataToStorage(nameStorage, options, token, filterCache);
            }

            setOptions(options);
            setLoading(false);
        } catch (_) {
            setOptions([]);
            setLoading(false);
        }
    }

    useEffect(() => {
        getDataLov();
        // eslint-disable-next-line react-hooks/exhaustive-deps
    }, [isSameData]);

    return { options, loading };
};
