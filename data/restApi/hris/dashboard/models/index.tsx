import { PaginationResponseModel } from '@core-models/pagination/paginationResponse.model';

export interface JenisKelaminResponseModel {
    success: boolean;
    message: string;
    data: DataJenisKelamin[];
    pagination?: PaginationResponseModel;
    service_url?: string;
}

export interface DataJenisKelamin {
    id: null | number;
    value: null | string;
    jumlah_pegawai: number;
}

export interface JabatanFungsionalResponseModel {
    success: boolean;
    message: string;
    data: DataJabatanFungsional[];
    pagination?: PaginationResponseModel;
    service_url?: string;
}

export interface DataJabatanFungsional {
    id: null | number;
    keterangan: null | string;
    jumlah: number;
}

export interface PendidikanResponseModel {
    success: boolean;
    message: string;
    data: DataPendidikan[];
    pagination?: PaginationResponseModel;
    service_url?: string;
}

export interface DataPendidikan {
    id: null | number;
    name: null | string;
    jumlah_staff: number;
}

export interface DatajumlahPegawai {
    total_pegawai: number;
    bulan: string;
    tahun: number;
    total_thp: number;
}

export interface KategoriCutiResponseModel {
    success: boolean;
    message: string;
    data: DataKategoriCuti[];
    pagination?: PaginationResponseModel;
    service_url?: string;
}

export interface DataKategoriCuti {
    id: null | number;
    jenis_cuti: number | string;
    total_cuti: number | string;
}

export interface DataJumlahTelat {
    direktorat_id: null | number;
    direktorat_nama: null | string;
    jumlah: number;
}

export interface DataJumlahGrafikTendik {
    is_akademik: null | number;
    kategori: null | string;
    jumlah: number;
}

