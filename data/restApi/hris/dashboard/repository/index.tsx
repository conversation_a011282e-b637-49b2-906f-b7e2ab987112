import { GlobalResponsePaginationModel } from '@core-models/global/globalResponse.model';
import { errorResponseData, getApiSimpul } from '@restApi/simpul';

export const getJenisKelaminDashboardRepository = async (token: string, data: any) => {
    try {
        const config = {
            url: 'hris/data-pegawai-by-jeniskelamin',
            token: token,
            data: data,
        };
        const result: GlobalResponsePaginationModel = await getApiSimpul(config);
        return result;
    } catch (error: any) {
        return errorResponseData(error);
    }
};

export const getDetailJenisKelaminRepository = async (id: any, token: string, filter: any) => {
    try {
        const config = {
            url: 'hris/list-pegawai-by-jeniskelamin/' + id,
            token: token,
            data: filter,
        };
        const result: GlobalResponsePaginationModel = await getApiSimpul(config);
        return result;
    } catch (error: any) {
        return errorResponseData(error);
    }
};

export const getJabatanFungsionalDashboardRepository = async (token: string, data: any) => {
    try {
        const config = {
            url: 'hris/data-pegawai-by-jabatanfungsional',
            token: token,
            data: data,
        };
        const result: GlobalResponsePaginationModel = await getApiSimpul(config);
        return result;
    } catch (error: any) {
        return errorResponseData(error);
    }
};

export const getDetailJabatanFungsionalRepository = async (id: any, token: string, filter: any) => {
    try {
        const config = {
            url: 'hris/list-pegawai-by-jabatanfungsional/' + id,
            token: token,
            data: filter,
        };
        const result: GlobalResponsePaginationModel = await getApiSimpul(config);
        return result;
    } catch (error: any) {
        return errorResponseData(error);
    }
};

export const getPendidikanDashboardRepository = async (token: string, data: any) => {
    try {
        const config = {
            url: 'hris/data-pegawai-by-pendidikan',
            token: token,
            data: data,
        };
        const result: GlobalResponsePaginationModel = await getApiSimpul(config);
        return result;
    } catch (error: any) {
        return errorResponseData(error);
    }
};

export const getDetailPendidikanRepository = async (id: any, token: string, filter: any) => {
    try {
        const config = {
            url: 'hris/list-pegawai-by-pendidikan/' + id,
            token: token,
            data: filter,
        };
        const result: GlobalResponsePaginationModel = await getApiSimpul(config);
        return result;
    } catch (error: any) {
        return errorResponseData(error);
    }
};

export const getJumlahPegawaiDashboardRepository = async (token: string, data: any) => {
    try {
        const config = {
            url: 'hris/payroll-take-home-pay-total',
            token: token,
            data: data,
        };
        const result: GlobalResponsePaginationModel = await getApiSimpul(config);
        return result;
    } catch (error: any) {
        return errorResponseData(error);
    }
};

export const getKategoriCutiRepository = async (token: string, data: any) => {
    try {
        const config = {
            url: 'hris/riwayat-cuti-total-by-jenis',
            token: token,
            data: data,
        };
        const result: GlobalResponsePaginationModel = await getApiSimpul(config);
        return result;
    } catch (error: any) {
        return errorResponseData(error);
    }
};

export const getAllListPegawaiTelatTidakRepository = async (token: string, data: any) => {
    try {
        const config = {
            url: 'hris/presensi-telat',
            token: token,
            data: data,
        };
        const result: GlobalResponsePaginationModel = await getApiSimpul(config);
        return result;
    } catch (error: any) {
        return errorResponseData(error);
    }
};

export const getAllListHabisMasaKontrakRepository = async (token: string, data: any) => {
    try {
        const config = {
            url: 'hris/kontrak-jabatan-pegawai-habis',
            token: token,
            data: data,
        };
        const result: GlobalResponsePaginationModel = await getApiSimpul(config);
        return result;
    } catch (error: any) {
        return errorResponseData(error);
    }
};

export const getJumlahTelatDashboardRepository = async (token: string, data: any) => {
    try {
        const config = {
            url: 'hris/jumlah-presensi-telat',
            token: token,
            data: data,
        };
        const result: GlobalResponsePaginationModel = await getApiSimpul(config);
        return result;
    } catch (error: any) {
        return errorResponseData(error);
    }
};

export const GrafikPegawaiByStatusKepegawaian = async (token: string, data: any) => {
    try {
        const config = {
            url: 'hris/grafik-akademik-non-akademik',
            token: token,
            data: data,
        };
        const result: GlobalResponsePaginationModel = await getApiSimpul(config);
        return result;
    } catch (error: any) {
        return errorResponseData(error);
    }
};

export const ListGrafikPegawaiByStatusKepegawaian = async (token: string, data: any) => {
    try {
        const config = {
            url: 'hris/master-pegawai',
            token: token,
            data: data,
        };
        const result: GlobalResponsePaginationModel = await getApiSimpul(config);
        return result;
    } catch (error: any) {
        return errorResponseData(error);
    }
};

export const getAllListMasaKerjaDashboardRepository = async (token: string, data: any) => {
    try {
        const config = {
            url: 'hris/master-pegawai-masakerja-dashboard',
            token: token,
            data: data,
        };
        const result: GlobalResponsePaginationModel = await getApiSimpul(config);
        return result;
    } catch (error: any) {
        return errorResponseData(error);
    }
};


export const getAllListMasaPensiunDashboardRepository = async (token: string, data: any) => {
    try {
        const config = {
            url: 'hris/master-pegawai-usia-dashboard',
            token: token,
            data: data,
        };
        const result: GlobalResponsePaginationModel = await getApiSimpul(config);
        return result;
    } catch (error: any) {
        return errorResponseData(error);
    }
};
