import { MasterBagianResponseModel, DetailMasterBagianResponseModel } from '../models';
import { errorResponseData, getApiSimpul, postApiSimpul, putApiSimpul, deleteApiSimpul } from '@restApi/simpul';

export const getAllListMasterBagianRepository = async (token: string, data: any) => {
    try {
        const config = {
            url: 'hris/master-bagian',
            token: token,
            data: data,
        };
        const result: MasterBagianResponseModel = await getApiSimpul(config);
        return result;
    } catch (error: any) {
        return errorResponseData(error);
    }
}; 

export const getDetailMasterBagianRepository = async (id: any, token: string) => {
    try {
      const config = {
        url: 'hris/master-bagian' + id,
        token: token,
        data: {},
      }
      const result: DetailMasterBagianResponseModel = await getApiSimpul(config)
      return result
    } catch (error: any) {
      return errorResponseData(error);
    }
  }
  
  export const createMasterBagianRepository = async (data: any, token: string) => {
    try {
      const config = {
        url: 'hris/master-bagian',
        token: token,
        data: data,
      }
      const result: any = await postApiSimpul(config)
      return result
    } catch (error: any) {
      return errorResponseData(error);
    }
  }
  
  export const updateMasterBagianRepository = async (
    data: any,
    id: string,
    token: string
  ) => {
    try {
      const config = {
        url: `hris/master-bagian/${id}`,
        token: token,
        data: data,
      }
      const result: any = await putApiSimpul(config)
      return result
    } catch (error: any) {
      return errorResponseData(error);
    }
  }
  
  export const deleteMasterBagianRepository = async (id: any, token: string) => {
    try {
      const config = {
        url: `hris/master-bagian/${id}`,
        token: token,
        data: {},
      }
      const result: any = await deleteApiSimpul(config)
      return result
    } catch (error: any) {
      return errorResponseData(error);
    }
  }