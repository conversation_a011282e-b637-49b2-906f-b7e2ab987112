
import { errorResponseData, getApiSimpul } from '@restApi/simpul';
import { MasterProgramStudiSevimaResponseModel } from '../models';

export const getAllListMasterProgramStudiSevimaRepository = async (token: string, data: any) => {
    try {
        const config = {
            url: 'api-sevima/master-program-studi',
            token: token,
            data: data,
        };
        const result: MasterProgramStudiSevimaResponseModel = await getApiSimpul(config);
        return result;
    } catch (error: any) {
        return errorResponseData(error);
    }
};